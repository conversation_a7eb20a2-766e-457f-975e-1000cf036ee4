# Утилиты для обработки ошибок

## error.ts

Универсальные функции для обработки 404 ошибок при SSR в Nuxt 3.

### Функции

#### `throw404Error(statusMessage?, fatal?, additionalProps?)`

Базовая функция для выброса 404 ошибки с правильным HTTP статусом.

**Параметры:**
- `statusMessage` (string, optional) - Сообщение об ошибке (по умолчанию "Page not found")
- `fatal` (boolean, optional) - Является ли ошибка фатальной (по умолчанию true)
- `additionalProps` (object, optional) - Дополнительные свойства ошибки

**Примеры:**
```typescript
// Простое использование
throw404Error();

// С кастомным сообщением
throw404Error("Страница не найдена");

// С дополнительными свойствами
throw404Error("Page not found", true, { type: "category" });
```

#### `validateDataOrThrow404<T>(data, statusMessage?, fatal?, additionalProps?)`

Проверяет существование данных и выбрасывает 404 ошибку если данные отсутствуют.

**Параметры:**
- `data` (T | null | undefined) - Данные для проверки
- `statusMessage` (string, optional) - Сообщение об ошибке
- `fatal` (boolean, optional) - Является ли ошибка фатальной
- `additionalProps` (object, optional) - Дополнительные свойства ошибки

**Примеры:**
```typescript
// Проверка данных страницы
validateDataOrThrow404(page?.value);

// Проверка с кастомным сообщением
validateDataOrThrow404(post?.data, "Пост не найден");

// Проверка с дополнительными свойствами
validateDataOrThrow404(
  data.value?.post && !error.value ? data.value.post : null,
  "Пост не найден",
  true,
  { type: "post" }
);
```

#### `validateConditionOrThrow404(condition, statusMessage?, fatal?, additionalProps?)`

Проверяет булево условие и выбрасывает 404 ошибку если условие false.

**Параметры:**
- `condition` (boolean) - Условие для проверки
- `statusMessage` (string, optional) - Сообщение об ошибке
- `fatal` (boolean, optional) - Является ли ошибка фатальной
- `additionalProps` (object, optional) - Дополнительные свойства ошибки

**Примеры:**
```typescript
// Проверка категории
validateConditionOrThrow404(
  !(route.params.category && !posts.value?.category),
  "Категория не найдена",
  true,
  { type: "category" }
);
```

### Преимущества использования

1. **Автоматическая установка HTTP статуса** - функции автоматически устанавливают правильный статус код 404 на сервере
2. **Единообразие** - все 404 ошибки обрабатываются одинаково
3. **Типобезопасность** - TypeScript поддержка с type assertions
4. **Гибкость** - поддержка дополнительных свойств ошибки
5. **Простота использования** - краткий и читаемый код

### Миграция с существующего кода

**Было:**
```typescript
if (!page?.value) {
  const event = useRequestEvent();
  setResponseStatus(event, 404);

  throw createError({
    statusCode: 404,
    statusMessage: "Page not found",
    fatal: true
  });
}
```

**Стало:**
```typescript
validateDataOrThrow404(page?.value);
```

**Было:**
```typescript
if (route.params.category && !posts.value?.category) {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "category"
  } as NuxtErrorWithType);
}
```

**Стало:**
```typescript
if (route.params.category && !posts.value?.category) {
  throw404Error("Page not found", true, { type: "category" });
}
```
