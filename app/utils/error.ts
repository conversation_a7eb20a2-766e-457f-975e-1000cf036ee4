/**
 * Универсальная функция для обработки 404 ошибок при SSR
 *
 * Автоматически устанавливает правильный HTTP статус код 404 на сервере
 * и выбрасывает Nuxt ошибку для корректной обработки.
 *
 * @param statusMessage - Сообщение об ошибке (по умолчанию "Page not found")
 * @param fatal - Является ли ошибка фатальной (по умолчанию true)
 * @param additionalProps - Дополнительные свойства ошибки (например, { type: "category" })
 *
 * @example
 * // Простое использование
 * throw404Error();
 *
 * @example
 * // С кастомным сообщением
 * throw404Error("Страница не найдена");
 *
 * @example
 * // С дополнительными свойствами
 * throw404Error("Page not found", true, { type: "category" });
 */
export function throw404Error(
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): never {
  if (import.meta.server) {
    const event = useRequestEvent();
    if (event) {
      setResponseStatus(event, 404);
    }
  }

  throw createError({
    statusCode: 404,
    statusMessage,
    fatal,
    ...additionalProps
  });
}

/**
 * Универсальная функция для проверки существования данных и выброса 404 ошибки
 *
 * Проверяет, что данные существуют (не null и не undefined), и если нет -
 * выбрасывает 404 ошибку с правильным HTTP статусом.
 *
 * @param data - Данные для проверки
 * @param statusMessage - Сообщение об ошибке (по умолчанию "Page not found")
 * @param fatal - Является ли ошибка фатальной (по умолчанию true)
 * @param additionalProps - Дополнительные свойства ошибки
 *
 * @example
 * // Проверка данных страницы
 * validateDataOrThrow404(page?.value);
 *
 * @example
 * // Проверка с кастомным сообщением
 * validateDataOrThrow404(post?.data, "Пост не найден");
 */
export function validateDataOrThrow404<T>(
  data: T | null | undefined,
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): asserts data is T {
  if (!data) {
    throw404Error(statusMessage, fatal, additionalProps);
  }
}

/**
 * Универсальная функция для проверки условия и выброса 404 ошибки
 *
 * Проверяет булево условие и если оно false - выбрасывает 404 ошибку.
 *
 * @param condition - Условие для проверки
 * @param statusMessage - Сообщение об ошибке (по умолчанию "Page not found")
 * @param fatal - Является ли ошибка фатальной (по умолчанию true)
 * @param additionalProps - Дополнительные свойства ошибки
 *
 * @example
 * // Проверка категории
 * validateConditionOrThrow404(
 *   !(route.params.category && !posts.value?.category),
 *   "Категория не найдена",
 *   true,
 *   { type: "category" }
 * );
 */
export function validateConditionOrThrow404(
  condition: boolean,
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): asserts condition {
  if (!condition) {
    throw404Error(statusMessage, fatal, additionalProps);
  }
}
