/**
 * Универсальная функция для обработки 404 ошибок при SSR
 *
 * Автоматически устанавливает правильный HTTP статус код 404 на сервере
 * и выбрасывает Nuxt ошибку для корректной обработки.
 *
 * @param statusMessage - Сообщение об ошибке (по умолчанию "Page not found")
 * @param fatal - Является ли ошибка фатальной (по умолчанию true)
 * @param additionalProps - Дополнительные свойства ошибки (например, { type: "category" })
 */
export function throw404Error(
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): never {
  if (import.meta.server) {
    const event = useRequestEvent();
    if (event) {
      setResponseStatus(event, 404);
    }
  }

  throw createError({
    statusCode: 404,
    statusMessage,
    fatal,
    ...additionalProps
  });
}

/**
 * Универсальная функция для проверки существования данных и выброса 404 ошибки
 */
export function validateDataOrThrow404<T>(
  data: T | null | undefined,
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): asserts data is T {
  if (!data) {
    throw404Error(statusMessage, fatal, additionalProps);
  }
}

/**
 * Универсальная функция для проверки условия и выброса 404 ошибки
 */
export function validateConditionOrThrow404(
  condition: boolean,
  statusMessage: string = "Page not found",
  fatal: boolean = true,
  additionalProps?: Record<string, any>
): asserts condition {
  if (!condition) {
    throw404Error(statusMessage, fatal, additionalProps);
  }
}
