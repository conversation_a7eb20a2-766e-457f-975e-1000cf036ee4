/**
 * Composable для обработки 404 ошибок при SSR
 */
export const useErrorHandling = () => {
  /**
   * Универсальная функция для обработки 404 ошибок при SSR
   */
  const throw404Error = (
    statusMessage: string = "Page not found",
    fatal: boolean = true,
    additionalProps?: Record<string, any>
  ): never => {
    if (import.meta.server) {
      const event = useRequestEvent();
      if (event) {
        setResponseStatus(event, 404);
      }
    }

    throw createError({
      statusCode: 404,
      statusMessage,
      fatal,
      ...additionalProps
    });
  };

  /**
   * Проверяет данные и выбрасывает 404 если данные отсутствуют
   */
  const validateDataOrThrow404 = <T>(
    data: T | null | undefined,
    statusMessage: string = "Page not found",
    fatal: boolean = true,
    additionalProps?: Record<string, any>
  ): asserts data is T => {
    if (!data) {
      throw404Error(statusMessage, fatal, additionalProps);
    }
  };

  return {
    throw404Error,
    validateDataOrThrow404
  };
};
