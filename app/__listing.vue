<template>
  <div class="py-3 md:py-4">
    <UContainer v-if="posts">
      <div class="flex flex-col gap-4 lg:flex-row lg:gap-6">
        <!-- Основной контент -->
        <div class="w-full">
          <!-- Верхняя секция -->
          <div class="space-y-3">
            <!-- Хлебные крошки -->
            <UBreadcrumb :items="breadcrumbs" />

            <!-- Категории (если нет города) -->
            <FiltersCategories v-if="!posts?.city" />

            <!-- Фильтры по типам -->
            <div v-if="posts.filters?.types?.values?.length" class="flex flex-wrap gap-1.5">
              <UButton
                size="xs"
                :to="{ params: { type: '' }, query: route.query }"
                :color="route.params?.type ? 'neutral' : 'primary'"
                variant="soft"
              >
                Все
              </UButton>
              <UButton
                v-for="type in posts.filters.types?.values"
                :key="type.value"
                size="xs"
                :to="getTypeTo(type)"
                :color="route.params?.type === type.value ? 'primary' : 'neutral'"
                variant="soft"
              >
                {{ type.label }}
              </UButton>
            </div>

            <!-- SEO заголовок и описание -->
            <div class="space-y-2">
              <h1 class="text-xl leading-tight font-bold md:text-2xl">
                {{ h1 }}
              </h1>
              <p class="text-sm text-[var(--ui-text-muted)]">
                <template v-if="posts.seo?.content">
                  {{ posts.seo.content }}
                </template>
                <template v-else>
                  {{ pageDescription }}
                </template>
              </p>
            </div>

            <!-- Алерты -->
            <AlertsBase />
          </div>

          <!-- Липкая панель фильтров -->
          <div class="sticky top-0 z-10 -mx-4 px-4 md:mx-0 md:px-0">
            <div
              class="flex gap-2 border-b border-gray-200 bg-white/95 py-3 backdrop-blur-sm dark:border-gray-800 dark:bg-gray-900/95"
            >
              <div class="flex flex-wrap gap-2">
                <FiltersList :filters="posts.filters" />
              </div>
            </div>
          </div>

          <!-- Счетчик и сортировка -->
          <div
            v-if="posts.meta.total > 0"
            class="mt-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between"
          >
            <p class="text-sm font-semibold md:text-base">
              Объявлений в разделе: {{ posts.meta.total }}
            </p>
            <FiltersOrderBy />
          </div>

          <!-- Пустое состояние -->
          <div v-if="(!posts?.data?.length || posts.is_empty) && status === 'success'" class="my-8">
            <div class="flex flex-col items-center text-center">
              <img class="mb-4 h-20 w-20" alt="gunpost.ru" src="/images/error.png" />
              <h3 class="mb-2 text-lg font-semibold">Нет подходящих предложений</h3>
              <p v-if="posts?.data?.length" class="text-[var(--ui-text-muted)]">
                Подобрали для вас предложения из соседних городов
              </p>
              <p v-else class="text-[var(--ui-text-muted)]">
                Смягчите фильтры, чтобы увидеть больше.
              </p>
            </div>
          </div>

          <!-- Сетка карточек -->
          <div class="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
            <template v-if="status === 'pending'">
              <Skeleton v-for="i in 21" :key="`skeleton-${i}`" />
            </template>
            <template v-else>
              <CardItem
                v-for="(item, n) in posts?.data"
                :key="`card-${n}-${item.id}`"
                :position="n"
                :item="item"
              />
            </template>
          </div>

          <!-- Похожие объявления -->
          <div
            v-if="posts?.other?.length"
            class="mt-6 border-t border-gray-200 pt-4 dark:border-gray-800"
          >
            <div class="mb-3 space-y-1.5">
              <h3 class="text-base font-semibold">Похожие объявления</h3>
              <p class="text-sm text-[var(--ui-text-muted)]">
                Подобрали для вас предложения из соседних городов
              </p>
            </div>

            <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              <CardItem
                v-for="(item, n) in posts?.other"
                :key="`other-${n}-${item.id}`"
                :position="n"
                :item="item"
              />
            </div>
          </div>

          <!-- Пагинация -->
          <Pagination
            :meta="posts.meta"
            :page="page"
            :disabled="status === 'pending'"
            class="mt-6"
            @set-page="setPage"
          />
        </div>

        <!-- Боковая панель -->
        <aside class="w-full space-y-4 lg:w-110">
          <!-- Новости -->
          <NewsWidget />

          <!-- Популярные обсуждения -->
          <div v-if="false" class="space-y-4">
            <h3 class="text-lg font-semibold">Популярные обсуждения</h3>

            <div class="space-y-3">
              <ULink
                to="/"
                class="block space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 transition-colors hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <h4 class="line-clamp-2 leading-tight font-medium">
                  Вопрос по эволюции патронов кольцевого воспламенения
                </h4>
                <p class="line-clamp-2 text-sm text-[var(--ui-text-muted)]">
                  Поиск по форуму нашел, что в ранних патронах Флобера не было выраженной закраины,
                  а только расширение, чтобы патрон не проваливался в ствол и было чего сминать
                  курку
                </p>
                <UUser
                  size="xs"
                  name="Ротмистр Чачу"
                  description="28-6-2024 12:38"
                  :avatar="{ src: 'https://i.pravatar.cc/50?u=Ротмистр Чачу' }"
                />
              </ULink>

              <ULink
                to="/"
                class="block space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 transition-colors hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <h4 class="line-clamp-2 leading-tight font-medium">
                  Перествол ВПО-209 366ТКМ в СКС 7, 62
                </h4>
                <p class="line-clamp-2 text-sm text-[var(--ui-text-muted)]">
                  Имею такой балласт в виде ВПО-209 который в свое время взял для стажа. В этой
                  связи вопрос - возможна ли его легальная модернизация до
                </p>
                <UUser
                  size="xs"
                  name="jacker2000"
                  description="3-2-2025 12:03"
                  :avatar="{ src: 'https://i.pravatar.cc/50?u=jacker2000' }"
                />
              </ULink>
            </div>

            <UButton size="sm" block variant="soft"> Все обсуждения </UButton>
          </div>

          <!-- VIP объявления -->
          <AdsVip v-if="posts?.vipAds?.length" :items="posts.vipAds" />
        </aside>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import type { PostsResponse, NuxtErrorWithType } from "~/types/listing";
// import { throw404Error } from "~/composables/useErrorHandling";

const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page || 1);

async function getRouteCacheKeyBase64() {
  const path = route.path;
  const query = new URLSearchParams(route.query).toString();
  const rawKey = `${path}?${query}:${page.value}`;
  const buffer = await crypto.subtle.digest("SHA-256", new TextEncoder().encode(rawKey));
  return btoa(String.fromCharCode(...new Uint8Array(buffer)));
}

function cleanParams(params: Record<string, any>): Record<string, any> {
  return Object.fromEntries(
    Object.entries(params).filter(
      ([, value]) =>
        value !== null && value !== undefined && (typeof value !== "string" || value.trim() !== "")
    )
  );
}

const { data: posts, status } = await useAsyncData<PostsResponse>(
  await getRouteCacheKeyBase64(),
  () =>
    client("/posts", {
      params: cleanParams({
        category: route.params?.category,
        city: route.params?.city,
        types: route.params?.type,
        page: page.value,
        path: route.path,
        ...route.query
      })
    }),
  {
    watch: [route]
  }
);

const { title, description, ogTitle, ogDescription, pageDescription, h1 } = useSeoFromFilters(
  posts,
  route
);

useSeoMeta({
  title,
  ogTitle,
  description,
  ogDescription
});

function setPage(value: number) {
  page.value = value;
}

function getTypeTo(type: { label: string; value: string }) {
  delete route.query.page;
  return {
    params: {
      ...route.params,
      type: type.value
    },
    query: route.query
  };
}

// if (route.params.category && !posts.value?.category) {
//   throw404Error("Page not found", true, { type: "category" });
// }

// if (route.params.category === "hunting" && route.params.type && !posts.value?.filters?.gun_types) {
//   throw404Error("Page not found", true, { type: "type" });
// }

// if (route.params.category !== "hunting" && route.params.type && !posts.value?.filters?.types) {
//   throw404Error("Page not found", true, { type: "type" });
// }

const breadcrumbs = ref([
  {
    label: "Главная",
    to: "/"
  }
]);

if (posts.value?.category?.name && posts.value?.city?.value) {
  breadcrumbs.value.push({
    label: posts.value.category.name,
    to: `/${posts.value.category.slug}`
  });
  breadcrumbs.value.push({
    label: posts.value.city.name,
    to: `/${posts.value.city.value}/${posts.value.category.slug}`
  });
} else if (posts.value?.category?.name) {
  breadcrumbs.value.push({
    label: posts.value.category.name,
    to: `/${posts.value.category.slug}`
  });
  breadcrumbs.value.push({
    label: "Вся Россия",
    to: `/${posts.value.category.slug}`
  });
}

if (posts.value?.filters?.types && posts.value?.city?.value && route.params.type) {
  const current = posts.value.filters.types.values.find((v) => v.value === route.params.type);
  breadcrumbs.value.push({
    label: current.label,
    to: `/${posts.value.city.value}/${posts.value.category.slug}/${current.value}`
  });
} else if (posts.value?.filters?.types && route.params.type) {
  const current = posts.value.filters.types.values.find((v) => v.value === route.params.type);
  breadcrumbs.value.push({
    label: current.label,
    to: `/${posts.value.category.slug}/${current.value}`
  });
}
</script>
