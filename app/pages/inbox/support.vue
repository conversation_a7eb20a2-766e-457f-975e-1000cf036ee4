<script setup lang="ts">
import type { NuxtErrorWithType } from "~/types/listing";
import { throw404Error } from "~/utils/error";

const client = useSanctumClient();

const { data } = await useAsyncData<{ id: string }>("chats.support", () =>
  client("/chats/support")
);
if (data.value?.id) {
  await navigateTo(`/inbox/${data.value.id}`);
} else {
  throw404Error("Page not found", true, { type: "support" });
}
</script>
