export default defineEventHandler(async (event) => {
  // Пропускаем статические ресурсы и API
  if (event.node.req.url?.includes('/_nuxt/') || 
      event.node.req.url?.includes('/api/') ||
      event.node.req.url?.includes('/__nuxt_error')) {
    return
  }

  // Перехватываем writeHead для установки правильного статус кода
  const originalWriteHead = event.node.res.writeHead
  const originalEnd = event.node.res.end
  
  event.node.res.writeHead = function(statusCode: number, statusMessage?: string | any, headers?: any) {
    // Если это ошибка 404 и статус код 200, исправляем его
    if (statusCode === 200 && event.context.nuxtError?.statusCode) {
      statusCode = event.context.nuxtError.statusCode
      statusMessage = event.context.nuxtError.statusMessage || (statusCode === 404 ? 'Not Found' : 'Error')
    }
    
    return originalWriteHead.call(this, statusCode, statusMessage, headers)
  }
  
  // Перехватываем end для проверки содержимого
  event.node.res.end = function(chunk?: any, encoding?: any) {
    // Проверяем содержимое на наличие страницы ошибки 404
    if (typeof chunk === 'string' && chunk.includes('Страница не найдена') && this.statusCode === 200) {
      this.statusCode = 404
      this.statusMessage = 'Not Found'
    }
    
    return originalEnd.call(this, chunk, encoding)
  }
})
