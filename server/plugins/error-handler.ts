export default defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook('render:response', (response, { event }) => {
    // Проверяем, является ли это страницей ошибки 404
    if (response.statusCode === 200 && typeof response.body === 'string') {
      if (response.body.includes('Страница не найдена') || 
          response.body.includes('404') && response.body.includes('error')) {
        response.statusCode = 404
        response.statusMessage = 'Not Found'
        
        // Также устанавливаем в оригинальном response
        if (event.node.res && !event.node.res.headersSent) {
          event.node.res.statusCode = 404
          event.node.res.statusMessage = 'Not Found'
        }
      }
    }
  })
  
  nitroApp.hooks.hook('error', (error, { event }) => {
    if (error.statusCode && event.node.res && !event.node.res.headersSent) {
      event.node.res.statusCode = error.statusCode
      event.node.res.statusMessage = error.statusMessage || (error.statusCode === 404 ? 'Not Found' : 'Error')
    }
  })
})
